package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

/**
 * Screen for managing detailed town settings.
 */
public class TownSettingsScreen extends Screen {
    private final Screen parent;
    private final Town town;
    
    // Panel dimensions (responsive)
    private int panelWidth;
    private int panelHeight;
    private int minPanelWidth = 400;
    private int minPanelHeight = 300;
    private int maxPanelWidth = 600;
    private int maxPanelHeight = 450;
    
    // UI elements
    private TextFieldWidget townNameField;
    private TextFieldWidget townDescriptionField;
    private TextFieldWidget maxPlayersField;
    
    // Settings state
    private boolean isOpen;

    // Button layout variables
    private int buttonY;
    private int buttonWidth;
    private int buttonSpacing;
    private int buttonStartX;
    private int saveButtonX;
    private int cancelButtonX;
    private int resetButtonX;
    private int buttonHeight;

    // Status message
    private Text statusText = Text.empty();
    private int statusColor = 0xFFFFFF;
    private long statusTime = 0;

    // Scrolling support
    private int scrollOffset = 0;
    private int maxScrollOffset = 0;
    private int contentHeight = 0;
    private int visibleHeight = 0;
    
    public TownSettingsScreen(Screen parent, Town town) {
        super(Text.literal("Town Settings"));
        this.parent = parent;
        this.town = town;
        
        // Load current settings
        loadTownSettings();
    }
    
    private void loadTownSettings() {
        if (town == null) return;

        try {
            // Load settings from TownSettingsManager (persisted settings)
            java.util.Map<String, Object> townSettings = com.pokecobble.town.config.TownSettingsManager.getTownSettings(town.getId());

            // Load isOpen setting from persisted data, fallback to town's JoinType if not found
            if (townSettings.containsKey("isOpen")) {
                isOpen = (Boolean) townSettings.get("isOpen");
                Pokecobbleclaim.LOGGER.info("TownSettingsScreen: Loaded isOpen setting from persisted data: " + isOpen + " for town " + town.getName());
            } else {
                // Fallback to town's current JoinType if no persisted setting exists
                isOpen = town.getJoinType() == Town.JoinType.OPEN;
                Pokecobbleclaim.LOGGER.info("TownSettingsScreen: No persisted isOpen setting found, using town JoinType: " + isOpen + " (" + town.getJoinType() + ") for town " + town.getName());
            }

            // Also log what the town object currently says for comparison
            Pokecobbleclaim.LOGGER.info("TownSettingsScreen: Town object JoinType is: " + town.getJoinType() + ", UI will show isOpen: " + isOpen);

            // Load other settings when they're implemented in the UI
            // allowPublicBuilding = (Boolean) townSettings.getOrDefault("allowPublicBuilding", false);
            // enablePvP = (Boolean) townSettings.getOrDefault("enablePvP", false);
            // allowMobSpawning = (Boolean) townSettings.getOrDefault("allowMobSpawning", true);
            // enableTownChat = (Boolean) townSettings.getOrDefault("enableTownChat", true);
            // showTownInList = (Boolean) townSettings.getOrDefault("showTownInList", true);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error loading town settings for UI: " + e.getMessage());
            // Fallback to town's current properties on error
            isOpen = town.getJoinType() == Town.JoinType.OPEN;
        }
    }
    
    @Override
    protected void init() {
        super.init();

        // Calculate responsive panel dimensions
        calculatePanelDimensions();

        // Calculate scrolling dimensions
        calculateScrollDimensions();

        // Calculate panel position
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Calculate responsive field dimensions
        int fieldWidth = Math.min(200, panelWidth - 140);
        int smallFieldWidth = Math.min(80, panelWidth - 200);

        // Calculate scrolled positions for content area
        int contentAreaY = topY + 25; // After header
        int scrolledContentY = contentAreaY - scrollOffset;

        // Calculate label position to match render method
        int labelX = leftX + 15;
        int fieldX = labelX + 60; // Position fields right after labels with some spacing

        // Town Name field (positioned in scrollable content)
        townNameField = new TextFieldWidget(this.textRenderer, fieldX, scrolledContentY + 35, fieldWidth, 18, Text.literal("Town Name"));
        townNameField.setMaxLength(32);
        townNameField.setText(town.getName());
        addDrawableChild(townNameField);

        // Town Description field (positioned in scrollable content)
        townDescriptionField = new TextFieldWidget(this.textRenderer, fieldX, scrolledContentY + 60, fieldWidth, 18, Text.literal("Description"));
        townDescriptionField.setMaxLength(100);
        townDescriptionField.setText(town.getDescription());
        addDrawableChild(townDescriptionField);

        // Max Players field (positioned in scrollable content)
        maxPlayersField = new TextFieldWidget(this.textRenderer, fieldX, scrolledContentY + 85, smallFieldWidth, 18, Text.literal("Max Players"));
        maxPlayersField.setMaxLength(3);
        maxPlayersField.setText(String.valueOf(town.getMaxPlayers()));
        addDrawableChild(maxPlayersField);

        // Store button layout for custom rendering (buttons stay at bottom, not scrolled)
        buttonY = topY + panelHeight - 35;
        buttonWidth = 80;
        buttonSpacing = 10;
        int totalButtonWidth = (buttonWidth * 3) + (buttonSpacing * 2);
        buttonStartX = leftX + (panelWidth - totalButtonWidth) / 2;

        // Store button positions for click handling
        saveButtonX = buttonStartX;
        cancelButtonX = buttonStartX + buttonWidth + buttonSpacing;
        resetButtonX = buttonStartX + (buttonWidth + buttonSpacing) * 2;
        buttonHeight = 18;
    }

    private void calculatePanelDimensions() {
        // Calculate responsive panel size based on screen dimensions
        panelWidth = Math.max(minPanelWidth, Math.min(maxPanelWidth, (int)(width * 0.7)));
        panelHeight = Math.max(minPanelHeight, Math.min(maxPanelHeight, (int)(height * 0.8)));

        // Ensure minimum size for usability
        if (panelWidth < minPanelWidth) panelWidth = minPanelWidth;
        if (panelHeight < minPanelHeight) panelHeight = minPanelHeight;

        // Ensure panel fits on screen with margins
        if (panelWidth > width - 40) panelWidth = width - 40;
        if (panelHeight > height - 40) panelHeight = height - 40;
    }

    private void calculateScrollDimensions() {
        // Calculate total content height needed
        int headerHeight = 25; // Header
        int basicFieldsHeight = 25 + 25 + 30; // Name, Desc, Max fields
        int settingsHeaderHeight = 18; // Settings title
        int currentTogglesHeight = 20; // 1 toggle (Open to Join)
        int buttonAreaHeight = 35; // Button area at bottom
        int margins = 40; // Top and bottom margins

        contentHeight = headerHeight + basicFieldsHeight + settingsHeaderHeight + currentTogglesHeight + buttonAreaHeight + margins;

        // Calculate visible height (panel height minus button area)
        visibleHeight = panelHeight - buttonAreaHeight;

        // Calculate maximum scroll offset
        maxScrollOffset = Math.max(0, contentHeight - visibleHeight);

        // Ensure scroll offset is within bounds
        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScrollOffset));
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render background
        this.renderBackground(context);

        // Recalculate dimensions in case of resize
        calculatePanelDimensions();
        calculateScrollDimensions();

        // Calculate panel position
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Draw panel background with rounded corners effect
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + panelHeight, 0xE0101010, 0xE0202030);

        // Draw header with gradient (always visible, not scrolled)
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + 25, 0xB0303050, 0xB0404060);

        // Draw title
        String title = "Settings - " + (town.getName().length() > 15 ? town.getName().substring(0, 12) + "..." : town.getName());
        context.drawCenteredTextWithShadow(this.textRenderer, title,
                leftX + panelWidth / 2, topY + 8, 0xFFFFFF);

        // Enable scissor test for scrollable content area
        int contentAreaY = topY + 25;
        int contentAreaHeight = panelHeight - 60; // Leave space for header and buttons
        context.enableScissor(leftX, contentAreaY, leftX + panelWidth, contentAreaY + contentAreaHeight);

        // Calculate scrolled positions
        int scrolledY = contentAreaY - scrollOffset;
        int currentY = scrolledY + 10;
        int labelX = leftX + 15;

        // Draw basic settings section
        context.drawTextWithShadow(this.textRenderer, "Name:", labelX, currentY + 4, 0xCCCCCC);
        currentY += 25;

        context.drawTextWithShadow(this.textRenderer, "Desc:", labelX, currentY + 4, 0xCCCCCC);
        currentY += 25;

        context.drawTextWithShadow(this.textRenderer, "Max:", labelX, currentY + 4, 0xCCCCCC);
        currentY += 30;

        // Draw advanced settings section with compact layout
        context.drawTextWithShadow(this.textRenderer, Text.literal("Settings").formatted(Formatting.BOLD),
                labelX, currentY, 0xFFFFFF);
        currentY += 18;

        // Draw current settings
        drawCompactToggleSetting(context, leftX + 20, currentY, "Open to Join", isOpen, mouseX, mouseY);

        // Disable scissor test
        context.disableScissor();

        // Draw scrollbar if needed
        if (maxScrollOffset > 0) {
            drawScrollbar(context, leftX, topY, mouseX, mouseY);
        }

        // Draw modern buttons
        drawModernButtons(context, mouseX, mouseY);

        // Draw status message
        if (statusText != null && !statusText.getString().isEmpty() &&
            System.currentTimeMillis() - statusTime < 3000) {
            context.drawCenteredTextWithShadow(this.textRenderer, statusText,
                    width / 2, topY - 15, statusColor);
        }

        super.render(context, mouseX, mouseY, delta);
    }
    
    private void drawCompactToggleSetting(DrawContext context, int x, int y, String label, boolean enabled, int mouseX, int mouseY) {
        // Calculate responsive toggle position
        int maxLabelWidth = panelWidth - 120; // Leave space for toggle and margins
        int toggleX = x + Math.min(140, maxLabelWidth);
        int toggleWidth = 32; // Smaller toggle
        int toggleHeight = 14; // Smaller height

        // Draw label (truncate if too long)
        String displayLabel = label;
        int labelWidth = this.textRenderer.getWidth(label);
        if (labelWidth > maxLabelWidth - 20) {
            // Truncate label to fit
            while (this.textRenderer.getWidth(displayLabel + "...") > maxLabelWidth - 20 && displayLabel.length() > 3) {
                displayLabel = displayLabel.substring(0, displayLabel.length() - 1);
            }
            displayLabel += "...";
        }

        context.drawTextWithShadow(this.textRenderer, displayLabel, x, y + 2, 0xFFFFFF);

        // Check if mouse is hovering
        boolean isHovered = mouseX >= toggleX && mouseX <= toggleX + toggleWidth &&
                           mouseY >= y && mouseY <= y + toggleHeight;

        // Draw toggle background with rounded corners effect
        int bgColor = enabled ? 0xFF4CAF50 : 0xFF616161; // Green if enabled, darker gray if disabled
        if (isHovered) {
            bgColor = enabled ? 0xFF66BB6A : 0xFF757575; // Lighter when hovered
        }

        // Draw toggle background
        context.fill(toggleX, y, toggleX + toggleWidth, y + toggleHeight, bgColor);

        // Draw toggle slider with better positioning
        int sliderSize = 10;
        int sliderX = enabled ? toggleX + toggleWidth - sliderSize - 2 : toggleX + 2;
        int sliderY = y + 2;
        context.fill(sliderX, sliderY, sliderX + sliderSize, sliderY + sliderSize, 0xFFFFFFFF);

        // Draw compact status indicator
        String statusText = enabled ? "✓" : "✗";
        int statusColor = enabled ? 0x55FF55 : 0xFF5555;
        context.drawTextWithShadow(this.textRenderer, statusText, toggleX + toggleWidth + 8, y + 2, statusColor);
    }

    private void drawScrollbar(DrawContext context, int panelX, int panelY, int mouseX, int mouseY) {
        // Scrollbar dimensions
        int scrollbarWidth = 6;
        int scrollbarX = panelX + panelWidth - scrollbarWidth - 2;
        int scrollbarAreaY = panelY + 25; // After header
        int scrollbarAreaHeight = panelHeight - 60; // Leave space for header and buttons

        // Draw scrollbar track
        context.fill(scrollbarX, scrollbarAreaY, scrollbarX + scrollbarWidth, scrollbarAreaY + scrollbarAreaHeight, 0x40000000);

        // Calculate scrollbar thumb position and size
        float scrollRatio = (float) scrollOffset / maxScrollOffset;
        float thumbSizeRatio = (float) visibleHeight / contentHeight;
        int thumbHeight = Math.max(20, (int) (scrollbarAreaHeight * thumbSizeRatio));
        int thumbY = scrollbarAreaY + (int) ((scrollbarAreaHeight - thumbHeight) * scrollRatio);

        // Check if mouse is over scrollbar
        boolean isHovered = mouseX >= scrollbarX && mouseX <= scrollbarX + scrollbarWidth &&
                           mouseY >= scrollbarAreaY && mouseY <= scrollbarAreaY + scrollbarAreaHeight;

        // Draw scrollbar thumb
        int thumbColor = isHovered ? 0x80FFFFFF : 0x60FFFFFF;
        context.fill(scrollbarX, thumbY, scrollbarX + scrollbarWidth, thumbY + thumbHeight, thumbColor);
    }

    private void drawModernButtons(DrawContext context, int mouseX, int mouseY) {
        // Save button
        boolean saveHovered = mouseX >= saveButtonX && mouseX <= saveButtonX + buttonWidth &&
                             mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
        drawModernButton(context, saveButtonX, buttonY, buttonWidth, buttonHeight, 0xFF4CAF50, saveHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Save",
                saveButtonX + buttonWidth / 2, buttonY + 5, 0xFFFFFF);

        // Cancel button
        boolean cancelHovered = mouseX >= cancelButtonX && mouseX <= cancelButtonX + buttonWidth &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
        drawModernButton(context, cancelButtonX, buttonY, buttonWidth, buttonHeight, 0xFF757575, cancelHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Cancel",
                cancelButtonX + buttonWidth / 2, buttonY + 5, 0xFFFFFF);

        // Reset button
        boolean resetHovered = mouseX >= resetButtonX && mouseX <= resetButtonX + buttonWidth &&
                              mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
        drawModernButton(context, resetButtonX, buttonY, buttonWidth, buttonHeight, 0xFFFF9800, resetHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Reset",
                resetButtonX + buttonWidth / 2, buttonY + 5, 0xFFFFFF);
    }

    /**
     * Draws a modern button background with gradient and subtle 3D effect.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, boolean isActive) {
        // Extract RGB components
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;

        // Adjust colors based on state
        if (!isActive) {
            // Desaturate and darken for inactive buttons
            int avg = (r + g + b) / 3;
            r = (r + avg) / 2;
            g = (g + avg) / 2;
            b = (b + avg) / 2;
            r = r * 3/4;
            g = g * 3/4;
            b = b * 3/4;
        } else if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Create colors for gradient
        int topColor = ((r) << 16) | ((g) << 8) | (b) | 0xFF000000;
        int bottomColor = ((r * 3/4) << 16) | ((g * 3/4) << 8) | (b * 3/4) | 0xFF000000;

        // Draw gradient background
        context.fillGradient(x, y, x + width, y + height, topColor, bottomColor);

        // Draw subtle 3D effect
        int highlightColor = 0x30FFFFFF; // Subtle white highlight
        int shadowColor = 0x30000000; // Subtle shadow

        // Top highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, highlightColor);
        // Left highlight
        context.fill(x + 1, y + 1, x + 2, y + height - 1, highlightColor);

        // Bottom shadow
        context.fill(x + 2, y + height - 2, x + width - 1, y + height - 1, shadowColor);
        // Right shadow
        context.fill(x + width - 2, y + 2, x + width - 1, y + height - 2, shadowColor);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            int leftX = (width - panelWidth) / 2;
            int topY = (height - panelHeight) / 2;

            // Check for button clicks
            if (mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                // Save button
                if (mouseX >= saveButtonX && mouseX <= saveButtonX + buttonWidth) {
                    SoundUtil.playButtonClickSound();
                    saveSettings();
                    return true;
                }
                // Cancel button
                if (mouseX >= cancelButtonX && mouseX <= cancelButtonX + buttonWidth) {
                    SoundUtil.playButtonClickSound();
                    this.close();
                    return true;
                }
                // Reset button
                if (mouseX >= resetButtonX && mouseX <= resetButtonX + buttonWidth) {
                    SoundUtil.playButtonClickSound();
                    resetToDefaults();
                    return true;
                }
            }

            // Check if click is in scrollable content area
            int contentAreaY = topY + 25;
            int contentAreaHeight = panelHeight - 60;

            if (mouseY >= contentAreaY && mouseY <= contentAreaY + contentAreaHeight) {
                // Calculate scrolled toggle positions to match render method
                int scrolledY = contentAreaY - scrollOffset;
                int currentY = scrolledY + 10 + 25 + 25 + 30 + 18; // After basic fields and settings header

                // Check toggle click for the only implemented setting
                if (checkCompactToggleClick(mouseX, mouseY, leftX + 20, currentY)) {
                    isOpen = !isOpen;
                    SoundUtil.playButtonClickSound();
                    return true;
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    private boolean checkCompactToggleClick(double mouseX, double mouseY, int x, int y) {
        int maxLabelWidth = panelWidth - 120;
        int toggleX = x + Math.min(140, maxLabelWidth);
        int toggleWidth = 32;
        int toggleHeight = 14;
        return mouseX >= toggleX && mouseX <= toggleX + toggleWidth &&
               mouseY >= y && mouseY <= y + toggleHeight;
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        if (maxScrollOffset > 0) {
            // Calculate scroll amount (negative amount = scroll down, positive = scroll up)
            int scrollAmount = (int) (-amount * 20); // 20 pixels per scroll step

            // Update scroll offset
            int newScrollOffset = scrollOffset + scrollAmount;
            scrollOffset = Math.max(0, Math.min(newScrollOffset, maxScrollOffset));

            // Reinitialize to update widget positions
            this.clearChildren();
            this.init();

            return true;
        }
        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle keyboard scrolling
        if (maxScrollOffset > 0) {
            int scrollAmount = 0;

            // Arrow keys for scrolling
            if (keyCode == 264) { // Down arrow
                scrollAmount = 20;
            } else if (keyCode == 265) { // Up arrow
                scrollAmount = -20;
            } else if (keyCode == 266) { // Page Up
                scrollAmount = -visibleHeight / 2;
            } else if (keyCode == 267) { // Page Down
                scrollAmount = visibleHeight / 2;
            }

            if (scrollAmount != 0) {
                int newScrollOffset = scrollOffset + scrollAmount;
                scrollOffset = Math.max(0, Math.min(newScrollOffset, maxScrollOffset));

                // Reinitialize to update widget positions
                this.clearChildren();
                this.init();

                return true;
            }
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    private void saveSettings() {
        try {
            // Validate input
            String newName = townNameField.getText().trim();
            String newDescription = townDescriptionField.getText().trim();
            String maxPlayersText = maxPlayersField.getText().trim();
            
            if (newName.isEmpty()) {
                setStatus("Town name cannot be empty!", Formatting.RED);
                return;
            }
            
            int maxPlayers;
            try {
                maxPlayers = Integer.parseInt(maxPlayersText);
                if (maxPlayers < 1 || maxPlayers > 100) {
                    setStatus("Max players must be between 1 and 100!", Formatting.RED);
                    return;
                }
            } catch (NumberFormatException e) {
                setStatus("Invalid number for max players!", Formatting.RED);
                return;
            }
            
            // Update basic town properties
            town.setName(newName);
            town.setDescription(newDescription);
            town.setMaxPlayers(maxPlayers);
            town.setJoinType(isOpen ? Town.JoinType.OPEN : Town.JoinType.CLOSED);

            // Save settings using TownSettingsManager for persistence
            com.pokecobble.town.config.TownSettingsManager.setTownSetting(town.getId(), "isOpen", isOpen);

            // Save other settings when they're implemented in the UI
            // com.pokecobble.town.config.TownSettingsManager.setTownSetting(town.getId(), "allowPublicBuilding", allowPublicBuilding);
            // com.pokecobble.town.config.TownSettingsManager.setTownSetting(town.getId(), "enablePvP", enablePvP);
            // com.pokecobble.town.config.TownSettingsManager.setTownSetting(town.getId(), "allowMobSpawning", allowMobSpawning);
            // com.pokecobble.town.config.TownSettingsManager.setTownSetting(town.getId(), "enableTownChat", enableTownChat);
            // com.pokecobble.town.config.TownSettingsManager.setTownSetting(town.getId(), "showTownInList", showTownInList);

            // Save town (this will also trigger town data persistence)
            TownManager.getInstance().saveTown(town);

            Pokecobbleclaim.LOGGER.info("Saved town settings: isOpen=" + isOpen + " for town " + town.getName());
            
            setStatus("Settings saved successfully!", Formatting.GREEN);
            
            // Close screen after a short delay
            MinecraftClient.getInstance().execute(() -> {
                try {
                    Thread.sleep(1000);
                    this.close();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving town settings: " + e.getMessage());
            setStatus("Error saving settings!", Formatting.RED);
        }
    }
    
    private void resetToDefaults() {
        isOpen = true;

        maxPlayersField.setText("20");

        setStatus("Reset to default settings", Formatting.YELLOW);
    }
    
    private void setStatus(String message, Formatting formatting) {
        statusText = Text.literal(message).formatted(formatting);
        statusTime = System.currentTimeMillis();
        
        // Set color based on formatting
        if (formatting == Formatting.RED) {
            statusColor = 0xFF5555;
        } else if (formatting == Formatting.GREEN) {
            statusColor = 0x55FF55;
        } else if (formatting == Formatting.YELLOW) {
            statusColor = 0xFFFF55;
        } else {
            statusColor = 0xFFFFFF;
        }
    }
    
    @Override
    public void close() {
        this.client.setScreen(parent);
    }
    
    @Override
    public boolean shouldPause() {
        return false;
    }
}
