package com.pokecobble.town.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.logging.ErrorLogger;
import com.pokecobble.town.logging.ErrorLogger.ErrorSeverity;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.fabricmc.loader.api.FabricLoader;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.UUID;

/**
 * Base network manager for handling packet registration and dispatch.
 * Provides common functionality for all network operations.
 */
public class NetworkManager {

    /**
     * Registers all network handlers for the mod.
     * This should be called during mod initialization.
     */
    public static void registerHandlers() {
        // Register client-side handlers
        if (isClient()) {
            registerClientHandlers();
        }

        // Register server-side handlers
        registerServerHandlers();
    }

    /**
     * Registers client-side packet handlers.
     * This is only called on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void registerClientHandlers() {
        Pokecobbleclaim.LOGGER.info("Registering client-side network handlers");

        // Register handlers from each network component
        com.pokecobble.town.network.town.TownNetworkHandler.registerClientHandlers();
        com.pokecobble.town.network.chunk.ChunkNetworkHandler.registerClientHandlers();
        com.pokecobble.town.network.player.PlayerNetworkHandler.registerClientHandlers();
        com.pokecobble.town.network.player.PlayerDataSynchronizer.registerClientHandlers();
        com.pokecobble.town.network.ErrorLogNetworkHandler.registerClientHandlers();
        com.pokecobble.town.network.election.ElectionNetworkHandler.registerClientHandlers();
        com.pokecobble.town.network.town.TownDataSynchronizer.registerClientHandlers();
        com.pokecobble.town.network.chunk.ChunkDataSynchronizer.registerClientHandlers();
        com.pokecobble.town.network.town.ClaimHistorySynchronizer.registerClientHandlers();
        com.pokecobble.town.network.town.TownImageSynchronizer.registerClientHandlers();
        com.pokecobble.town.network.town.InviteNetworkHandler.registerClientHandlers();
        com.pokecobble.town.network.town.TownInviteHandler.registerClientHandlers();
        com.pokecobble.town.network.money.MoneyNetworkHandler.registerClientHandlers();
        com.pokecobble.phone.network.PhoneDataSynchronizer.registerClientHandlers();
        com.pokecobble.town.invitation.TownInvitationSynchronizer.registerClientHandlers();
        com.pokecobble.notification.ServerNotificationSynchronizer.registerClientHandlers();
        com.pokecobble.town.image.EnhancedImageSynchronizer.registerClientHandlers();
        com.pokecobble.town.permission.PermissionSynchronizer.registerClientHandlers();
        com.pokecobble.config.ConfigSynchronizer.registerClientHandlers();

        // Initialize client status manager
        com.pokecobble.status.ClientStatusManager.getInstance();
    }

    /**
     * Registers server-side packet handlers.
     */
    private static void registerServerHandlers() {
        Pokecobbleclaim.LOGGER.info("Registering server-side network handlers");

        // Register handlers from each network component
        com.pokecobble.town.network.town.TownNetworkHandler.registerServerHandlers();
        com.pokecobble.town.network.chunk.ChunkNetworkHandler.registerServerHandlers();
        com.pokecobble.town.network.player.PlayerNetworkHandler.registerServerHandlers();
        com.pokecobble.town.network.player.PlayerDataSynchronizer.registerServerHandlers();
        com.pokecobble.town.network.election.ElectionNetworkHandler.registerServerHandlers();
        com.pokecobble.town.network.town.TownDataSynchronizer.registerServerHandlers();
        com.pokecobble.town.network.town.TownImageSynchronizer.registerServerHandlers();
        com.pokecobble.town.network.chunk.ChunkDataSynchronizer.registerServerHandlers();
        com.pokecobble.town.network.town.ClaimHistorySynchronizer.registerServerHandlers();
        com.pokecobble.town.network.town.InviteNetworkHandler.registerServerHandlers();
        com.pokecobble.town.network.town.TownInviteHandler.registerServerHandlers();
        com.pokecobble.town.network.money.MoneyNetworkHandler.registerServerHandlers();
        com.pokecobble.phone.network.PhoneDataSynchronizer.registerServerHandlers();
        com.pokecobble.town.invitation.TownInvitationSynchronizer.registerServerHandlers();
        com.pokecobble.notification.ServerNotificationSynchronizer.registerServerHandlers();
        com.pokecobble.town.image.EnhancedImageSynchronizer.registerServerHandlers();
        com.pokecobble.town.permission.PermissionSynchronizer.registerServerHandlers();
        com.pokecobble.config.ConfigSynchronizer.registerServerHandlers();
        com.pokecobble.status.RealTimeStatusManager.registerServerHandlers();

        // Reset all player town and chunk versions
        com.pokecobble.town.network.town.TownDataSynchronizer.resetAllPlayerTownVersions();
        com.pokecobble.town.network.chunk.ChunkDataSynchronizer.resetAllPlayerChunkVersions();
        com.pokecobble.town.network.town.ClaimHistorySynchronizer.resetAllPlayerTownHistoryVersions();
    }

    /**
     * Sends a packet to the server.
     * This is only called on the client side.
     *
     * @param packetId The packet identifier
     * @param buf The packet data
     */
    @Environment(EnvType.CLIENT)
    public static void sendToServer(Identifier packetId, PacketByteBuf buf) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Send packet to server
            ClientPlayNetworking.send(packetId, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending packet to server: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error sending packet to server: " + e.getMessage(),
                e,
                "Network",
                ErrorSeverity.ERROR
            );
        }
    }

    /**
     * Sends a packet to a specific player.
     * This is only called on the server side.
     *
     * @param player The player to send the packet to
     * @param packetId The packet identifier
     * @param buf The packet data
     */
    public static void sendToPlayer(ServerPlayerEntity player, Identifier packetId, PacketByteBuf buf) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Send packet to player
            ServerPlayNetworking.send(player, packetId, buf);
        } catch (Exception e) {
            String playerName = player != null ? player.getName().getString() : "unknown";
            UUID playerId = player != null ? player.getUuid() : null;

            Pokecobbleclaim.LOGGER.error("Error sending packet to player " + playerName + ": " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error sending packet to player: " + e.getMessage(),
                e,
                "Network",
                ErrorSeverity.ERROR,
                playerName,
                playerId
            );
        }
    }

    /**
     * Sends a packet to all players in a town.
     * This is only called on the server side.
     *
     * @param server The server instance
     * @param town The town whose players should receive the packet
     * @param packetId The packet identifier
     * @param buf The packet data
     */
    public static void sendToTownPlayers(MinecraftServer server, com.pokecobble.town.Town town, Identifier packetId, PacketByteBuf buf) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Get all online players in the town
            for (UUID playerId : town.getPlayers()) {
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    ServerPlayNetworking.send(player, packetId, buf);
                }
            }
        } catch (Exception e) {
            String townName = town != null ? town.getName() : "unknown";

            Pokecobbleclaim.LOGGER.error("Error sending packet to players in town " + townName + ": " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error sending packet to town players: " + e.getMessage() + " (Town: " + townName + ")",
                e,
                "Network",
                ErrorSeverity.ERROR,
                null, // No specific player
                null  // No specific player UUID
            );
        }
    }

    /**
     * Sends a packet to all players on the server.
     * This is only called on the server side.
     *
     * @param server The server instance
     * @param packetId The packet identifier
     * @param buf The packet data
     */
    public static void sendToAll(MinecraftServer server, Identifier packetId, PacketByteBuf buf) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Send packet to all players
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                ServerPlayNetworking.send(player, packetId, buf);
            }
        } catch (Exception e) {
            int playerCount = server != null ? server.getPlayerManager().getCurrentPlayerCount() : 0;

            Pokecobbleclaim.LOGGER.error("Error sending packet to all players (" + playerCount + " online): " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error sending packet to all players: " + e.getMessage() + " (" + playerCount + " players online)",
                e,
                "Network",
                ErrorSeverity.ERROR,
                null, // No specific player
                null  // No specific player UUID
            );
        }
    }

    /**
     * Creates a new packet buffer.
     *
     * @return A new packet buffer
     */
    public static PacketByteBuf createPacket() {
        return PacketByteBufs.create();
    }



    /**
     * Checks if the current environment is client-side.
     *
     * @return True if client-side, false otherwise
     */
    private static boolean isClient() {
        return FabricLoader.getInstance().getEnvironmentType() == EnvType.CLIENT;
    }
}
