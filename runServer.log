
> Configure project :
Fabric Loom: 1.10.5

> Task :compileJava
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/client/ClaimToolDataSynchronizer.java:80: warning: [unchecked] unchecked cast
                Map<String, Object> eventData = (Map<String, Object>) data;
                                                                      ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:156: warning: [unchecked] unchecked cast
            return (Map<String, Object>) categoryPrefs;
                                         ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:204: warning: [unchecked] unchecked conversion
            Map<String, Object> preferences = gson.fromJson(jsonObject, Map.class);
                                                           ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:348: warning: [unchecked] unchecked conversion
            Map<String, Object> config = gson.fromJson(jsonValue, Map.class);
                                                      ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:60: warning: [unchecked] unchecked conversion
            Map<String, Map<String, Object>> loaded = gson.fromJson(reader, Map.class);
                                                                   ^
  required: Map<String,Map<String,Object>>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:192: warning: [unchecked] unchecked cast
            return (T) value;
                       ^
  required: T
  found:    Object
  where T is a type-variable:
    T extends Object declared in method <T>getPreference(String,String,T)
6 warnings

> Task :processResources UP-TO-DATE
> Task :classes
> Task :jar
> Task :compileTestJava NO-SOURCE
> Task :processIncludeJars UP-TO-DATE
> Task :sourcesJar
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE
> Task :test NO-SOURCE
> Task :validateAccessWidener NO-SOURCE
> Task :check UP-TO-DATE
> Task :cleanRun
> Task :generateLog4jConfig UP-TO-DATE
> Task :generateRemapClasspath UP-TO-DATE
> Task :generateDLIConfig UP-TO-DATE
> Task :configureLaunch UP-TO-DATE
> Task :remapJar FAILED
> Task :remapSourcesJar

[Incubating] Problems report is available at: file:///home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/reports/problems/problems-report.html

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':remapJar'.
> A failure occurred while executing net.fabricmc.loom.task.RemapJarTask$RemapAction
   > Failed to remap, java.util.concurrent.CompletionException: java.lang.RuntimeException: error analyzing /com/pokecobble/town/command/TownElectionCommand.class from /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/devlibs/pokecobbleclaim-1.0.0-dev.jar

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 8s
11 actionable tasks: 6 executed, 5 up-to-date
